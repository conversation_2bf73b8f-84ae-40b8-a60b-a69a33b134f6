import { sveltekit } from '@sveltejs/kit/vite';
import tailwindcss from '@tailwindcss/vite';
import { defineConfig } from 'vite';
import devtoolsJson from 'vite-plugin-devtools-json';
import { visualizer } from 'rollup-plugin-visualizer';
import { analyzer } from 'vite-bundle-analyzer';
import { compression } from 'vite-plugin-compression2';

export default defineConfig({
	plugins: [
		tailwindcss(),
		sveltekit(),
		devtoolsJson(),
		// Compression plugins for production builds
		compression({
			algorithms: ['gzip'],
			exclude: [/\.(br)$/, /\.(gz)$/]
		}),
		compression({
			algorithms: ['brotliCompress'],
			exclude: [/\.(br)$/, /\.(gz)$/]
		}),
		// Bundle analysis plugins (only in build mode)
		...(typeof process !== 'undefined' && process.env.ANALYZE
			? [
					visualizer({
						filename: 'dist/stats.html',
						open: true,
						gzipSize: true,
						brotliSize: true
					}),
					analyzer({
						analyzerMode: 'static',
						openAnalyzer: false,
						fileName: 'dist/bundle-report.html'
					})
				]
			: [])
	],
	build: {
		// Optimize build performance and output
		target: 'esnext',
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true,
				drop_debugger: true
			}
		},
		rollupOptions: {
			output: {
				manualChunks: (id) => {
					// Split vendor chunks for better caching
					if (id.includes('node_modules')) {
						if (id.includes('svelte')) {
							return 'vendor-svelte';
						}
						if (id.includes('bits-ui') || id.includes('tailwind')) {
							return 'vendor-ui';
						}
						if (id.includes('@lucide')) {
							return 'vendor-icons';
						}
						return 'vendor';
					}
				}
			}
		},
		// Enable source maps for debugging
		sourcemap: true,
		// Performance budgets
		chunkSizeWarningLimit: 500, // Warn for chunks larger than 500kb
		assetsInlineLimit: 4096, // Inline assets smaller than 4kb
		// Report bundle size
		reportCompressedSize: true
	},
	// Optimize dependency pre-bundling
	optimizeDeps: {
		include: ['clsx', 'tailwind-merge'],
		exclude: ['@sveltejs/kit']
	},
	test: {
		projects: [
			{
				extends: './vite.config.ts',
				test: {
					name: 'client',
					environment: 'browser',
					browser: {
						enabled: true,
						provider: 'playwright',
						instances: [{ browser: 'chromium' }]
					},
					include: ['src/**/*.svelte.{test,spec}.{js,ts}'],
					exclude: ['src/lib/server/**'],
					setupFiles: ['./vitest-setup-client.ts']
				}
			},
			{
				extends: './vite.config.ts',
				test: {
					name: 'server',
					environment: 'node',
					include: ['src/**/*.{test,spec}.{js,ts}'],
					exclude: ['src/**/*.svelte.{test,spec}.{js,ts}']
				}
			}
		]
	}
});
